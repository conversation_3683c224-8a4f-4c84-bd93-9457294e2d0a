<template>
 <router-view />
</template>

<script setup>
// 生成随机uid并存入localStorage
function generateUid() {
  // 简单生成一个16位的随机字符串
  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let uid = '';
  for (let i = 0; i < 16; i++) {
    uid += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return uid;
}

// 检查本地是否已有uid，没有则生成
if (!localStorage.getItem('deviceCode1')) {
  localStorage.setItem('deviceCode1', generateUid());
}
// document.addEventListener('contextmenu', (e) => {
//   e.preventDefault();
//   e.stopPropagation();
//   return false;
// });
</script>

<style scoped>

</style>
<style>
  html,body{
    margin: 0;
    padding: 0;
    background: #fff;
  }
</style>
