<template>
  <div class="headerContent1">
    <img
      @click="goBack"
      src="../assets/cancel.png"
      alt="返回"
      class="back-btn1"
    />
    <span style="color: #000">题目管理</span>
  </div>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-card class="filter-container" shadow="never">
      <div style="margin-top: 15px">
        <el-form
          :inline="true"
          :model="listQuery"
          size="default"
          label-width="90px"
        >
          <el-form-item label="题目问题：">
            <el-input
              v-model="listQuery.keyword"
              class="input-width"
              placeholder="请输入题目问题"
              clearable
            />
          </el-form-item>
          <!-- <el-form-item label="所属题库：">
            <el-select
              v-model="listQuery.gameCategory"
              placeholder="请选择所属题库"
              clearable
              style="width: 145px"
            >
              <el-option
                v-for="category in gameCategoryOptions"
                :key="category"
                :label="category"
                :value="category"
              />
            </el-select>
          </el-form-item> -->
          <el-form-item label="题目类型：">
            <el-select
              v-model="listQuery.questionType"
              placeholder="请选择题目类型"
              clearable
              style="width: 145px"
            >
              <el-option label="判断题" value="判断题" />
              <el-option label="选择题" value="选择题" />
              <el-option label="问答题" value="问答题" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              size="default"
              @click="handleSearchList()"
            >
              查询搜索
            </el-button>
            <el-button
              style="margin-left: 10px"
              size="default"
              @click="handleResetSearch()"
            >
              重置
            </el-button>
          </el-form-item>
          <div style="height: 40px">
            <el-button
              size="mini"
              class="btn-add"
              @click="handleAddQuestion"
              style="float: right; margin-right: 16px"
            >
              添加题目
            </el-button>
          </div>
        </el-form>
      </div>
    </el-card>

    <!-- 操作区域 -->
    <!-- <el-card class="operate-container" shadow="never">
      <i class="el-icon-tickets" />
      <span>题目列表</span>
      <el-button
        size="mini"
        class="btn-add"
        @click="handleAddQuestion"
        style="float: right"
      >
        添加题目
      </el-button>
    </el-card> -->

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        v-loading="listLoading"
        ref="categoryTable"
        :data="list"
        :resizable="false"
        style="width: 100%"
        border
      >
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column
          prop="content"
          label="题目问题"
          width="300"
          align="center"
        />
        <el-table-column
          prop="gameCategory"
          label="所属题库"
          width="100"
          align="center"
        />
        <el-table-column
          prop="questionType"
          label="题目类型"
          width="120"
          align="center"
        />
        <el-table-column
          prop="correctAnswer"
          label="答案"
          width="120"
          align="center"
        />
        <el-table-column label="是否可用" width="120" align="center">
          <template #default="scope">
            <span
              :style="{
                color: scope.row.status ? '#13ce66' : '#ff4949',
                fontWeight: 'bold',
              }"
            >
              {{ scope.row.status ? "可用" : "不可用" }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="180" align="center">
          <template #default="scope">
            <span>{{ formatCreateTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleUpdateQuestion(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              size="mini"
              type="danger"
              @click="handleDeleteQuestion(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        :current-page.sync="listQuery.pageNum"
        :page-size="listQuery.pageSize"
        :page-sizes="[20, 40, 60]"
        :total="total"
        background
        layout="total, sizes,prev, pager, next,jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :total-text="`共 ${total} 条`"
      >
        <template #total> 共 {{ total }} 条 </template>
      </el-pagination>
    </div>

    <!-- 添加/编辑题目对话框 -->
    <el-dialog
      :title="isEdit ? '编辑题目' : '添加题目'"
      v-model="dialogVisible"
      width="800px"
    >
      <el-form
        ref="questionFormRef"
        :model="questionForm"
        :rules="questionRules"
        label-width="120px"
      >
        <!-- <el-form-item label="所属题库" prop="gameCategory">
          <el-input
               v-model="questionForm.gameCategory"
                placeholder="请输入所属题库 如：逆水寒手游、快问快答、DNF老玩家问答篇"
              />
        </el-form-item> -->
        <el-form-item label="题目问题" prop="content">
          <el-input
            v-model="questionForm.content"
            placeholder="请输入题目问题"
            type="textarea"
            :rows="3"
          />
        </el-form-item>
        <el-form-item
          class="upload-item-box-form"
          label="题目配图"
          prop="imageUrl"
        >
          <uploadSingle
            class="goods_cover_upload_answer"
            style="margin: 0px !important"
            :url-pic="questionForm.imageUrl"
            name-key="cover"
            @upSuccsessSingle="handleImageUpload"
          />
        </el-form-item>
        <el-form-item label="题目类型" prop="questionType">
          <el-select
            v-model="questionForm.questionType"
            placeholder="请选择题目类型"
            @change="handleTypeChange"
          >
            <el-option label="判断题" value="判断题" />
            <el-option label="选择题" value="选择题" />
            <el-option label="问答题" value="问答题" />
          </el-select>
        </el-form-item>

        <!-- 选择题选项 -->
        <div v-if="questionForm.questionType === '选择题'">
          <el-form-item label="选项" prop="options">
            <div
              v-for="(_, index) in questionForm.options"
              :key="index"
              style="margin-bottom: 10px; display: flex; align-items: center"
            >
              <span style="margin-right: 10px; min-width: 20px">
                {{ String.fromCharCode(65 + index) }}.
              </span>
              <el-input
                v-model="questionForm.options[index]"
                :placeholder="`选项${'ABCDEFGH'[index]}`"
                style="flex: 1; margin-right: 10px"
              />
              <el-button
                v-if="questionForm.options.length > 2"
                type="danger"
                :icon="Delete"
                size="mini"
                style="padding: 8px; margin-right: 20px"
                @click="removeOption(index)"
              />
            </div>
            <el-button
              type="primary"
              style="margin-left: 30px; margin-top: -10px"
              size="mini"
              @click="addOption"
              v-if="questionForm.options.length < 8"
            >
              添加选项
            </el-button>
          </el-form-item>
        </div>
        <el-form-item label="题目难度" prop="questionType">
          <el-select
            v-model="questionForm.difficulty"
            placeholder="请选择题目难度"
            style="width: 200px"
          >
            <el-option label="简单" value="简单" />
            <el-option label="中等" value="中等" />
            <el-option label="困难" value="困难" />
            <el-option label="史诗" value="史诗" />
          </el-select>
        </el-form-item>
        <el-form-item label="正确答案" prop="correctAnswer">
          <!-- 判断题 -->
          <el-radio-group
            v-if="questionForm.questionType === '判断题'"
            v-model="questionForm.correctAnswer"
          >
            <el-radio label="是">是</el-radio>
            <el-radio label="否">否</el-radio>
          </el-radio-group>

          <!-- 选择题 -->
          <el-input
            v-else-if="questionForm.questionType === '选择题'"
            v-model="questionForm.correctAnswer"
            placeholder="请输入正确答案"
          />

          <!-- 问答题 -->
          <el-input
            v-else-if="questionForm.questionType === '问答题'"
            v-model="questionForm.correctAnswer"
            placeholder="请输入参考答案"
            type="textarea"
            :rows="3"
          />
        </el-form-item>

        <el-form-item label="答案解析" prop="answerAnalysis">
          <el-input
            v-model="questionForm.answerAnalysis"
            placeholder="请输入答案解析（可选）"
            type="textarea"
            :rows="2"
          />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="questionForm.status"
            :active-value="true"
            :inactive-value="false"
            active-text="启用"
            inactive-text="禁用"
          >
          </el-switch>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :loading="confirmLoading"
        >
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { Delete } from "@element-plus/icons-vue";
import uploadSingle from "./components/upload.vue";
import { invoke } from "@tauri-apps/api/core";
import { ref, reactive, onMounted, onUnmounted, nextTick } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter, useRoute } from "vue-router";
const route = useRoute();
const router = useRouter();
function goBack() {
  router.push({ path: "/answerList" });
}
import {
  fetchQuestionList,
  createQuestion,
  updateQuestion,
  deleteQuestion,
  updateQuestionStatus,
  getGameCategories,
} from "@/api/answer";
// import uploadSingle from '@/components/uploadSingle/index1';

const defaultListQuery = {
  pageNum: 1,
  pageSize: 20,
  keyword: "",
  questionType: "",
  gameCategory: "",
};

const defaultQuestionForm = {
  content: "",
  questionType: "",
  gameCategory: "",
  correctAnswer: "",
  answerAnalysis: "",
  imageUrl: "",
  options: ["", ""],
  status: true,
};

// 响应式数据
const listLoading = ref(false);
const confirmLoading = ref(false);
const list = ref([]);
const total = ref(0);
const listQuery = reactive({ ...defaultListQuery });
const dialogVisible = ref(false);
const isEdit = ref(false);
const questionForm = ref({ ...defaultQuestionForm });
const gameCategoryOptions = ref([]);

// 表单引用
const questionFormRef = ref(null);

// 表单验证规则
const questionRules = {
  content: [
    { required: true, message: "请输入题目内容", trigger: "blur" },
    {
      min: 1,
      max: 500,
      message: "题目内容长度在1到500个字符",
      trigger: "blur",
    },
  ],
  questionType: [
    { required: true, message: "请选择题目类型", trigger: "change" },
  ],
  // gameCategory: [
  //   { required: true, message: "请输入所属题库", trigger: "blur" },
  // ],
  correctAnswer: [
    { required: true, message: "请输入正确答案", trigger: "blur" },
  ],
};

// 格式化时间过滤器函数
const formatCreateTime = (time) => {
  if (!time) return "";
  const date = new Date(time);
  return date.toLocaleString("zh-CN");
};

// 获取题目列表
const getList = async () => {
  listLoading.value = true;
  try {
    listQuery.gameCategory = categoryName.value;
    const response = await fetchQuestionList(listQuery);

    if (response.code == 200) {
      list.value = response.data.list || [];
      total.value = response.data.total || 0;
    }
  } catch (error) {
    console.error("获取题目列表失败", error);
  } finally {
    listLoading.value = false;
  }
};

// 搜索
const handleSearchList = () => {
  listQuery.pageNum = 1;
  getList();
};

// 重置搜索
const handleResetSearch = () => {
  Object.assign(listQuery, defaultListQuery);
  getList();
};

// 获取游戏分类选项
const getGameCategoryOptions = async () => {
  try {
    const response = await getGameCategories();
    if (response.code === 200) {
      gameCategoryOptions.value = response.data || [];
    }
  } catch (error) {
    ElMessage.error("获取所属题库失败");
  }
};

// 分页大小改变
const handleSizeChange = (val) => {
  listQuery.pageSize = val;
  listQuery.pageNum = 1;
  getList();
};

// 当前页改变
const handleCurrentChange = (val) => {
  listQuery.pageNum = val;
  getList();
};

// 添加题目
const handleAddQuestion = () => {
  isEdit.value = false;
  questionForm.value = { ...defaultQuestionForm };

  // 如果有题库参数，自动填入
  if (route.query.categoryName) {
    questionForm.value.gameCategory = categoryName.value;
  }

  dialogVisible.value = true;
  nextTick(() => {
    questionFormRef.value?.clearValidate();
  });
};

// 编辑题目
const handleUpdateQuestion = (row) => {
  isEdit.value = true;
  questionForm.value = { ...row };
  if (row.imageUrl == null) {
    questionForm.value.imageUrl = "";
  }
  // 处理选择题选项
  if (row.questionType === "选择题") {
    if (row.options && Array.isArray(row.options)) {
      questionForm.value.options = [...row.options];
    } else if (typeof row.options === "string") {
      try {
        questionForm.value.options = JSON.parse(row.options);
      } catch (e) {
        questionForm.value.options = ["", ""];
      }
    } else {
      questionForm.value.options = ["", ""];
    }
  } else {
    questionForm.value.options = [];
  }
  dialogVisible.value = true;
  nextTick(() => {
    questionFormRef.value?.clearValidate();
  });
};

// 删除题目
const handleDeleteQuestion = async (row) => {
  try {
    await ElMessageBox.confirm("确认要删除该题目吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });
    await deleteQuestion(row.id);
    ElMessage.success("删除成功");
    getList();
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除失败", error);
    }
  }
};

// 状态开关改变
const handleStatusChange = async (row) => {
  try {
    await updateQuestionStatus(row.id, row.status);
    ElMessage.success("状态更新成功");
  } catch (error) {
    row.status = !row.status;
    ElMessage.error("状态更新失败");
  }
};

// 题目类型改变
const handleTypeChange = () => {
  questionForm.value.correctAnswer = "";
  if (questionForm.value.questionType === "选择题") {
    questionForm.value.options = ["", ""];
  } else {
    questionForm.value.options = [];
  }
};

// 添加选项
const addOption = () => {
  questionForm.value.options.push("");
};

// 删除选项
const removeOption = (index) => {
  questionForm.value.options.splice(index, 1);
};

// 处理图片上传
const handleImageUpload = (imageUrl) => {
  console.log(imageUrl, 11111111);
  questionForm.value.imageUrl = imageUrl;
};

// 确认添加/编辑
const handleConfirm = () => {
  questionFormRef.value?.validate(async (valid) => {
    if (valid) {
      if (questionForm.value.questionType === "选择题") {
        if (
          !questionForm.value.options ||
          questionForm.value.options.length < 2
        ) {
          ElMessage.error("选择题至少需要填写两个选项");
          return;
        }
        const emptyOptions = questionForm.value.options.filter(
          (option) => !option || !option.trim()
        );
        if (emptyOptions.length > 0) {
          ElMessage.error("选项内容不能为空");
          return;
        }
      }

      const formData = { ...questionForm.value };
      formData.optionsList = formData.options;
      confirmLoading.value = true;

      try {
        if (isEdit.value) {
          await updateQuestion(questionForm.value.id, formData);
          ElMessage.success("编辑成功");
        } else {
          await createQuestion(formData);
          ElMessage.success("添加成功");
        }
        dialogVisible.value = false;
        getList();
      } catch (error) {
        console.error("操作失败", error);
      } finally {
        confirmLoading.value = false;
      }
    }
  });
};
let categoryName = ref("");
// 组件挂载时执行
onMounted(async () => {
  // 从路由参数中获取题库信息
  if (route.query.categoryName) {
    categoryName.value = route.query.categoryName;
  }
  // await invoke("set_window_width", { width: 1000 });

  getList();
  getGameCategoryOptions();
});
onUnmounted(async () => {
  // await invoke("set_window_width", { width: 500 });
});
</script>
<style>
.upload-item-box-form .el-form-item__content {
  margin-left: 0px !important;
  display: flex !important;
}
.upload-item-box-form .picUpload_wrap {
  display: flex !important;
}
.goods_cover_upload_answer .picUpload_wrap {
  width: 191.11px;
  height: 93.413px;
  border-radius: 20.567px;
  /* border: 1px dashed #969696; */
  position: relative;
  text-align: center;
  line-height: 93.413px;
  font-size: 28px;
  color: #969696;
  /* overflow: hidden; */
  /* background: #f7f7f7; */
}
.goods_cover_upload_answer .el-upload-list__item {
  width: 100%;
  height: 93.413px;
  border: none;
}
.goods_cover_upload_answer
  .el-upload-list__item-status-label
  .el-icon-upload-success {
  display: none;
}
.goods_cover_upload_answer .picUpload_wrap .el-upload--picture-card {
  width: 191.11px;
  height: 93.413px;
  line-height: 93.413px;
  background: #f7f7f7;
  border: 1px dashed #969696;
  border-radius: 20.567px;
}
</style>
<style scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.operate-container {
  margin-bottom: 20px;
  padding: 15px;
}

.operate-container .btn-add {
  margin-left: 10px;
}

.table-container {
  margin-bottom: 20px;
}

.pagination-container {
  text-align: center;
  padding: 20px 0;
}

.input-width {
  width: 140px;
}

.dialog-footer {
  text-align: right;
}
.el-form-item {
  margin-right: 10px;
}
.headerContent1 {
  display: flex;
  align-items: center;
  padding: 20px 20px 10px 20px;
  font-size: 20px;
  font-weight: bold;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}
.back-btn1 {
  width: 20px;
  height: 20px;
  margin-right: 15px;
  cursor: pointer;
}
</style>
