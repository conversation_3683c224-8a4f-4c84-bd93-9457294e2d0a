<template>
  <div class="headerContent1">
    <img
      @click="goBack"
      src="../assets/cancel.png"
      alt="返回"
      class="back-btn1"
    />
    <span style="color: #000">题库管理</span>
  </div>

  <div class="app-container">
    <!-- 搜索区域 -->
    <!-- <el-card class="filter-container" shadow="never">
      <div style="margin-top: 15px">
        <el-form
          :inline="true"
          :model="listQuery"
          size="default"
          label-width="90px"
        >
          <el-form-item label="题库名称：">
            <el-input
              v-model="listQuery.keyword"
              class="input-width"
              placeholder="请输入题库名称"
              clearable
            />
          </el-form-item>
          
          <el-form-item>
            <el-button
              type="primary"
              size="default"
              @click="handleSearchList()"
            >
              查询搜索
            </el-button>
            <el-button
              style="margin-left: 10px"
              size="default"
              @click="handleResetSearch()"
            >
              重置
            </el-button>
          </el-form-item>
          
          <div style="height: 40px">
            <el-button
              size="mini"
              class="btn-add"
              @click="handleAddCategory"
              style="float: right; margin-right: 16px"
            >
              添加题库
            </el-button>
          </div>
        </el-form>
      </div>
    </el-card> -->
    <div style="height: 40px">
      <el-button
        size="mini"
        class="btn-add"
        @click="handleAddCategory"
        style="float: right; margin-right: 16px"
      >
        添加题库
      </el-button>
      <el-button
        size="mini"
        class="btn-add"
        @click="handleImport"
        style="float: right; margin-right: 16px"
      >
        导入题库
      </el-button>
      <!-- <el-button
        size="mini"
        class="btn-add"
        @click="handleExport"
        style="float: right"
      >
        下载模版
      </el-button> -->
    </div>
    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        v-loading="listLoading"
        ref="categoryTable"
        :data="list"
        :resizable="false"
        style="width: 100%"
        border
      >
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="categoryName" label="题库名称" align="center" />
        <el-table-column label="创建时间" width="180" align="center">
          <template #default="scope">
            <span>{{ formatCreateTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="300" align="center" fixed="right">
          <template #default="scope">
            <el-button
              size="mini"
              type="success"
              @click="goQuestions(scope.row)"
            >
              题目管理
            </el-button>
            <el-button
              size="mini"
              type="primary"
              @click="handleUpdateCategory(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              size="mini"
              type="danger"
              @click="handleDeleteCategory(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <!-- <div class="pagination-container">
      <el-pagination
        :current-page.sync="listQuery.pageNum"
        :page-size="listQuery.pageSize"
        :page-sizes="[20, 40, 60]"
        :total="total"
        background
        layout="total, sizes,prev, pager, next,jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
        <template #total>
          共 {{ total }} 条
        </template>
      </el-pagination>
    </div> -->

    <!-- 添加/编辑题库对话框 -->
    <el-dialog
      :title="isEdit ? '编辑题库' : '添加题库'"
      v-model="dialogVisible"
      width="500px"
    >
      <el-form
        ref="categoryFormRef"
        :model="categoryForm"
        :rules="categoryRules"
        label-width="100px"
      >
        <el-form-item label="题库名称" prop="categoryName">
          <el-input
            v-model="categoryForm.categoryName"
            placeholder="请输入题库名称，如：地下城与勇士、英雄联盟"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :loading="confirmLoading"
        >
          确定
        </el-button>
      </div>
    </el-dialog>
    <!-- 导入题库对话框 -->
    <el-dialog title="导入题库" v-model="importDialogVisible" width="860px">
     <div>
      <ExportContent ref="exportRef"/>
     </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button
            type="primary"
            @click="importDialogVisible = false"
          >
            关闭
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, onUnmounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Delete, UploadFilled } from "@element-plus/icons-vue";
import { useRouter } from "vue-router";
import { invoke } from "@tauri-apps/api/core";
import { exportEx } from "../utils/utils";
import { generateAuthHeaders } from "../utils/utils";
import ExportContent from './index3.vue'
import {
  fetchCategoryList,
  createCategory,
  updateCategory,
  deleteCategory,
} from "@/api/answer";

const router = useRouter();

function goBack() {
  router.push({ path: "/" });
}

// 跳转到题目管理页面
function goQuestions(row) {
  router.push({
    path: "/questions",
    query: {
      categoryName: row.categoryName,
      categoryId: row.id,
    },
  });
}

const defaultListQuery = {
  //   pageNum: 1,
  //   pageSize: 20,
  //   keyword: "",
};

const defaultCategoryForm = {
  categoryName: "",
};

// 响应式数据
const listLoading = ref(false);
const confirmLoading = ref(false);
const list = ref([]);
const total = ref(0);
const listQuery = reactive({ ...defaultListQuery });
const dialogVisible = ref(false);
const isEdit = ref(false);
const categoryForm = ref({ ...defaultCategoryForm });

// 表单引用
const categoryFormRef = ref(null);

// 表单验证规则
const categoryRules = {
  categoryName: [
    { required: true, message: "请输入题库名称", trigger: "blur" },
    {
      min: 1,
      max: 50,
      message: "题库名称长度在1到50个字符",
      trigger: "blur",
    },
  ],
};


// 格式化时间过滤器函数
const formatCreateTime = (time) => {
  if (!time) return "";
  const date = new Date(time);
  return date.toLocaleString("zh-CN");
};

// 获取题库列表
const getList = async () => {
  listLoading.value = true;
  try {
    const response = await fetchCategoryList(listQuery);
    console.log(response, "题库列表");

    if (response.code === 200) {
      list.value = response.data || [];
      //   total.value = response.data.total || 0;
    }
  } catch (error) {
    console.error("获取题库列表失败", error);
    ElMessage.error("获取题库列表失败");
  } finally {
    listLoading.value = false;
  }
};

// 搜索
const handleSearchList = () => {
  listQuery.pageNum = 1;
  getList();
};

// 重置搜索
const handleResetSearch = () => {
  Object.assign(listQuery, defaultListQuery);
  getList();
};

// 分页大小改变
const handleSizeChange = (val) => {
  listQuery.pageSize = val;
  listQuery.pageNum = 1;
  getList();
};

// 当前页改变
const handleCurrentChange = (val) => {
  listQuery.pageNum = val;
  getList();
};

// 添加题库
const handleAddCategory = () => {
  isEdit.value = false;
  categoryForm.value = { ...defaultCategoryForm };
  dialogVisible.value = true;
  nextTick(() => {
    categoryFormRef.value?.clearValidate();
  });
};

// 编辑题库
const handleUpdateCategory = (row) => {
  isEdit.value = true;
  categoryForm.value = { ...row };
  dialogVisible.value = true;
  nextTick(() => {
    categoryFormRef.value?.clearValidate();
  });
};

// 删除题库
const handleDeleteCategory = async (row) => {
  try {
    await ElMessageBox.confirm(
      "确认要删除该题库吗？删除后该题库下的所有题目也将被删除！",
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    );

    await deleteCategory(row.id);
    ElMessage.success("删除成功");
    getList();
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除失败", error);
      ElMessage.error("删除失败");
    }
  }
};

// 确认添加/编辑
const handleConfirm = () => {
  categoryFormRef.value?.validate(async (valid) => {
    if (valid) {
      confirmLoading.value = true;

      try {
        if (isEdit.value) {
          await updateCategory(categoryForm.value.id, categoryForm.value);
          ElMessage.success("编辑成功");
        } else {
          await createCategory(categoryForm.value);
          ElMessage.success("添加成功");
        }
        dialogVisible.value = false;
        getList();
      } catch (error) {
        console.error("操作失败", error);
        ElMessage.error("操作失败");
      } finally {
        confirmLoading.value = false;
      }
    }
  });
};

// 添加导入相关的响应式数据
const importDialogVisible = ref(false);


let exportRef=ref("")
// 打开导入对话框
const handleImport = () => {
  importDialogVisible.value = true;
  // 这里调用了子组件的reset方法，确保子组件实现了reset方法，否则不会有任何效果
    if (exportRef.value && typeof exportRef.value.reset === "function") {
    exportRef.value.reset();
  } else {
    // 如果子组件没有reset方法，可以在这里打印提示，方便排查
    console.warn("子组件未实现reset方法，未能重置导入弹窗内容");
  }
};




// 组件挂载时执行
onMounted(async () => {
  getList();
  await invoke("set_window_width", { width: 1000 });
});
onUnmounted(async () => {
  await invoke("set_window_width", { width: 500 });
});
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.table-container {
  margin-bottom: 20px;
}

.pagination-container {
  text-align: center;
  padding: 20px 0;
}

.input-width {
  width: 200px;
}

.dialog-footer {
  text-align: right;
}

.el-form-item {
  margin-right: 10px;
}

.headerContent1 {
  display: flex;
  align-items: center;
  padding: 20px 20px 10px 20px;
  font-size: 20px;
  font-weight: bold;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.back-btn1 {
  width: 20px;
  height: 20px;
  margin-right: 15px;
  cursor: pointer;
}
.import-container {
  padding: 20px 0;
}

.import-tips {
  margin-bottom: 20px;
}

.import-tips .el-alert p {
  margin: 5px 0;
  line-height: 1.5;
}

.upload-area {
  margin-top: 20px;
}

.upload-area .el-upload-dragger {
  width: 100%;
  height: 180px;
}

.dialog-footer {
  text-align: right;
}
</style>
