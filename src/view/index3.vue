<template>
  <!-- 模板部分保持不变 -->
  <div class="excel-viewer">
    <div class="controls">
      <!-- 题库选择下拉框 -->
      <div class="category-select-wrapper">
        <el-select
          v-model="selectedGameCategory"
          placeholder="请先选择题库"
          clearable
          style="width: 200px"
          @change="handleCategoryChange"
        >
          <el-option
            v-for="category in gameCategoryOptions"
            :key="category.categoryName"
            :label="category.categoryName"
            :value="category.categoryName"
          />
        </el-select>
      </div>

      <div class="file-upload-wrapper">
        <input
          type="file"
          id="fileInput"
          @change="handleFile"
          accept=".xlsx,.xls"
          class="file-input-hidden"
          :disabled="!selectedGameCategory"
        />
        <label
          for="fileInput"
          class="file-upload-btn"
          :class="{ disabled: !selectedGameCategory }"
        >
          <svg
            class="upload-icon"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
          >
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="7,10 12,15 17,10"></polyline>
            <line x1="12" y1="15" x2="12" y2="3"></line>
          </svg>
          {{
            file
              ? file.name
              : selectedGameCategory
              ? "选择Excel文件"
              : "请先选择题库"
          }}
        </label>
      </div>

      <button
        class="parse-btn"
        @click="parseExcel"
        :disabled="!file || loading || !selectedGameCategory"
        :class="{ loading: loading }"
      >
        <svg v-if="loading" class="btn-spinner" viewBox="0 0 24 24">
          <circle
            cx="12"
            cy="12"
            r="10"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
          ></circle>
          <path
            d="M12 2 A10 10 0 0 1 22 12"
            stroke="currentColor"
            stroke-width="2"
            fill="none"
            stroke-linecap="round"
          ></path>
        </svg>
        <svg
          v-else
          class="parse-icon"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
        >
          <path
            d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"
          ></path>
          <polyline points="14,2 14,8 20,8"></polyline>
          <line x1="16" y1="13" x2="8" y2="13"></line>
          <line x1="16" y1="17" x2="8" y2="17"></line>
          <polyline points="10,9 9,9 8,9"></polyline>
        </svg>
        {{ loading ? loadingText : "上传" }}
      </button>
    </div>

    <!-- Loading 指示器 -->
    <div v-if="loading" class="loading-indicator">
      <div class="loading-spinner"></div>
      <span>{{ loadingText }}</span>
    </div>

    <div v-if="error" class="error">{{ error }}</div>
    <div v-if="successText" class="successText">{{ successText }}</div>
    <!-- 验证错误表格 -->
    <div v-if="validationErrors.length > 0" class="validation-errors">
      <h3 style="color: red">数据验证失败，以下数据不合规：</h3>
      <el-table
        :data="validationErrors"
        style="width: 100%; margin-bottom: 20px"
        border
        stripe
        height="300"
      >
        <el-table-column
          prop="rowIndex"
          label="行号"
          width="80"
          align="center"
        />
        <el-table-column
          prop="sheetName"
          label="工作表"
          width="120"
          align="center"
        />
        <el-table-column
          prop="content"
          label="题目问题"
          width="300"
          align="center"
        />
        <el-table-column prop="errors" label="错误信息" align="center">
          <template #default="scope">
            <div style="color: red">
              <div v-for="errorMsg in scope.row.errors" :key="errorMsg">
                {{ errorMsg }}
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 正常数据表格 -->
    <el-table
      v-if="parsedData.length > 0"
      ref="categoryTable"
      :data="parsedData"
      :resizable="false"
      style="width: 100%"
      border
      height="300"
    >
      <el-table-column prop="content" label="题目问题" align="center" />
      <el-table-column prop="image" label="配图" width="120" align="center">
        <template #default="scope">
          <div v-if="scope.row.imageUrl">
            <img
              :src="scope.row.imageUrl"
              style="max-width: 100px; max-height: 80px"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="difficulty"
        label="题目难度"
        width="90"
        align="center"
      />
      <el-table-column
        prop="questionType"
        label="题目类型"
        width="90"
        align="center"
      />
      <el-table-column
        prop="correctAnswer"
        label="正确答案"
        width="200"
        align="center"
      />
    </el-table>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import ExcelJS from "exceljs";
import JSZip from "jszip";
// 移除xml2js，使用原生DOMParser
import {
  stsTokenApi,
  batchCreateQuestions,
  getGameCategories,
} from "@/api/answer.js";
import { ElMessage } from "element-plus";

const file = ref(null);
const loading = ref(false);
const loadingText = ref("解析中..."); // 动态loading文本
const error = ref(null);
const successText = ref(null);
const sheets = ref([]);
const activeSheet = ref("");
const parsedData = ref([]); // 存储转换后的数组格式数据
const validationErrors = ref([]); // 存储验证错误的数据

// 题库选择相关
const selectedGameCategory = ref("");
const gameCategoryOptions = ref([]);

const activeSheetData = computed(() => {
  return sheets.value.find((sheet) => sheet.name === activeSheet.value);
});

function isImageCell(cell) {
  return cell?.type === "image" || cell?.formula?.includes("DISPIMG");
}

// 获取题库选项
const getGameCategoryOptions = async () => {
  try {
    const response = await getGameCategories();
    if (response.code === 200) {
      gameCategoryOptions.value = response.data || [];
    }
  } catch (error) {
    ElMessage.error("获取题库选项失败");
  }
};

// 题库选择改变事件
const handleCategoryChange = (value) => {
  // 清空文件选择和数据
  clearFileInput();
  sheets.value = [];
  parsedData.value = [];
  validationErrors.value = [];
  error.value = null;
  successText.value = null;
};

// 重置所有数据，包括错误表格、正确表格等
const reset = () => {
  clearFileInput();
  selectedGameCategory.value = "";
  sheets.value = [];
  parsedData.value = [];
  validationErrors.value = [];
  error.value = null;
  successText.value = null;
};
defineExpose({
  reset,
});
const handleFile = (e) => {
  if (!selectedGameCategory.value) {
    ElMessage.warning("请先选择题库");
    return;
  }

  file.value = e.target.files[0];
  error.value = null;
  successText.value = null;
  // 每次选择新文件时清空之前的数据
  sheets.value = [];
  parsedData.value = [];
  validationErrors.value = []; // 清空验证错误
};

// 清空文件输入的函数
const clearFileInput = () => {
  file.value = null;
  // 同时清空HTML input元素的值
  const fileInput = document.getElementById("fileInput");
  if (fileInput) {
    fileInput.value = "";
  }
};

// ✅ 上传题目数据到服务器
const uploadQuestionsToServer = async (questions) => {
  try {
    console.log("📤 开始上传题目数据到服务器...", questions);

    const response = await batchCreateQuestions(questions);
    clearFileInput();
    if (response && response.code === 200) {
      console.log("✅ 题目数据上传成功:", response);
      successText.value = response.message;
      // 可以在这里添加成功提示
      // ElMessage.success(`成功上传 ${questions.length} 道题目`);
    } else {
      throw new Error(response?.message || "上传失败");
    }
  } catch (err) {
    clearFileInput();
    console.error("❌ 题目数据上传失败:", err);
    throw new Error(`上传题目失败: ${err.message}`);
  }
};

// ✅ 数据预验证函数 - 在上传OSS之前验证数据
const preValidateExcelData = async (workbook) => {
  const errors = [];
  const parsedSheets = [];

  // 处理每个工作表进行预验证
  workbook.eachSheet((worksheet) => {
    console.log(`🔍 预验证工作表: ${worksheet.name}`);

    const sheetData = {
      name: worksheet.name,
      headers: [],
      jsonData: [],
    };

    // 提取表头
    const headerRow = worksheet.getRow(1);
    if (headerRow?.values) {
      sheetData.headers = headerRow.values.slice(1);
    }

    const jsonRows = [];

    // 处理数据行进行验证
    worksheet.eachRow({ includeEmpty: true }, (row, rowIndex) => {
      if (rowIndex === 1) return; // 跳过标题行

      const jsonRowData = {};
      let hasContent = false;

      // 检查这一行是否为空行
      row.eachCell({ includeEmpty: true }, (cell) => {
        if (cell.value || cell.formula || cell.text) {
          hasContent = true;
        }
      });

      // 如果整行都是空的，跳过这一行
      if (!hasContent) {
        return;
      }

      // 提取行数据
      row.eachCell({ includeEmpty: true }, (cell, colIndex) => {
        const headerName = sheetData.headers[colIndex - 1] || `列${colIndex}`;

        // 对于图片单元格，暂时标记为图片类型
        if (cell.formula?.includes("DISPIMG")) {
          jsonRowData[headerName] = { type: "image", placeholder: true };
        } else {
          const cellValue = cell.value || cell.text || null;
          jsonRowData[headerName] = cellValue;
        }
      });

      jsonRows.push(jsonRowData);
    });

    sheetData.jsonData = jsonRows;
    parsedSheets.push(sheetData);
  });

  // 进行数据验证
  parsedSheets.forEach((sheet) => {
    const questionType = sheet.name;

    sheet.jsonData.forEach((row, rowIndex) => {
      const questionData = {
        gameCategory: selectedGameCategory.value, // 使用页面选择的题库
        content: row["问题"] || "",
        difficulty: row["题目难度"] || "",
        questionType: questionType,
        correctAnswer:
          typeof row["正确答案"] === "string"
            ? row["正确答案"].replace(/\n/g, "")
            : row["正确答案"] || "",
        answerAnalysis: row["答案解析"] || "",
      };

      // 验证必填字段
      const validationErrors = validateQuestionData(
        questionData,
        row,
        questionType
      );

      if (validationErrors.length > 0) {
        errors.push({
          rowIndex: rowIndex + 2, // +2 因为第一行是表头，数组索引从0开始
          sheetName: sheet.name,
          content: questionData.content || "(空)",
          errors: validationErrors,
        });
      }
    });
  });

  return {
    hasErrors: errors.length > 0,
    errors: errors,
    parsedSheets: parsedSheets,
  };
};

const parseExcel = async () => {
  if (!selectedGameCategory.value) {
    error.value = "请先选择题库";
    return;
  }
  if (!file.value) {
    error.value = "请先选择文件";
    return;
  }

  loading.value = true;
  loadingText.value = "正在上传文件...";
  error.value = null;
  successText.value = null;
  sheets.value = [];
  validationErrors.value = [];

  try {
    const arrayBuffer = await readFileAsArrayBuffer(file.value);
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(arrayBuffer);

    // 1. 数据预验证
    const preValidationResult = await preValidateExcelData(workbook);
    if (preValidationResult.hasErrors) {
      validationErrors.value = preValidationResult.errors;
      error.value = `数据验证失败，共发现 ${preValidationResult.errors.length} 条不合规数据`;
      clearFileInput();
      return;
    }

    // 2. 基于解析Excel内部XML结构的图片处理方案
    console.log("🖼️ 开始基于XML结构解析的图片处理流程...");

    // 2.1 尝试解析Excel内部XML结构获取DISPIMG映射
    const dispimgMapping = await parseExcelInternalStructure(
      arrayBuffer,
      workbook
    );

    // 2.2 收集所有图片信息
    const allImages = [];

    for (let i = 0; i < workbook.media.length; i++) {
      const media = workbook.media[i];
      const buffer = media.buffer;
      const hash = generateImageHash(buffer);

      allImages.push({
        id: i,
        media,
        hash,
        buffer,
        type: media.type,
      });

      console.log(`📷 收集图片: ID=${i}, Hash=${hash}, Type=${media.type}`);
    }

    // 2.2 建立工作表图片位置映射
    const sheetImagePositionMap = new Map();
    workbook.eachSheet((worksheet) => {
      const positionMap = new Map();
      const images = worksheet.getImages();

      console.log(`🔍 工作表 ${worksheet.name} 的图片信息:`, images);

      images.forEach((img) => {
        const row = img.range.tl.row + 1;
        const col = img.range.tl.col + 1;
        const key = `${row}-${col}`;

        // 关键修复：img.imageId 直接对应 workbook.model.media 的索引
        // 但我们需要映射到我们的 allImages 数组索引
        if (img.imageId < allImages.length) {
          positionMap.set(key, img.imageId);
          console.log(
            `🗺️ 位置映射: ${worksheet.name} ${key} -> 图片ID ${img.imageId}`
          );
        } else {
          console.warn(
            `⚠️ 图片ID ${img.imageId} 超出范围，总图片数: ${allImages.length}`
          );
        }
      });

      console.log(
        `📍 工作表 ${worksheet.name} 的位置映射:`,
        Array.from(positionMap.entries())
      );
      sheetImagePositionMap.set(worksheet.name, positionMap);
    });

    // 2.3 上传图片并建立ID映射
    const imageIdToUrlMap = new Map();
    const uploadedHashes = new Map();

    // 先标记所有重复图片
    const hashToFirstImageId = new Map();
    for (const image of allImages) {
      if (!hashToFirstImageId.has(image.hash)) {
        hashToFirstImageId.set(image.hash, image.id);
      }
    }

    // 并行上传所有唯一图片
    const uploadPromises = [];
    const uniqueImages = [];

    // 收集需要上传的唯一图片
    for (const image of allImages) {
      const firstImageId = hashToFirstImageId.get(image.hash);
      if (image.id === firstImageId) {
        // 这是该hash的第一个图片，需要上传
        uniqueImages.push(image);
      }
    }

    // 上传唯一图片
    for (const image of uniqueImages) {
      uploadPromises.push(
        (async () => {
          try {
            const fileName =
              image.media.name || `excel_image_${Date.now()}_${image.id}.png`;
            const fileObject = arrayBufferToFile(
              image.buffer,
              fileName,
              image.type
            );
            const uploadResult = await uploadImageToOSS(fileObject, fileName);

            const imageUrl =
              uploadResult?.url ||
              `data:${image.type};base64,${arrayBufferToBase64(image.buffer)}`;

            // 将URL分配给所有具有相同hash的图片
            for (const img of allImages) {
              if (img.hash === image.hash) {
                imageIdToUrlMap.set(img.id, imageUrl);
                console.log(
                  `🔗 映射图片: ID=${img.id} -> URL=${imageUrl.substring(
                    0,
                    50
                  )}...`
                );
              }
            }

            uploadedHashes.set(image.hash, imageUrl);
            console.log(
              `✅ 图片${image.id}上传成功，共享给${
                allImages.filter((img) => img.hash === image.hash).length
              }个位置`
            );
          } catch (e) {
            console.error(`❌ 图片${image.id}上传失败:`, e);
            // 为所有具有相同hash的图片设置null
            for (const img of allImages) {
              if (img.hash === image.hash) {
                imageIdToUrlMap.set(img.id, null);
              }
            }
          }
        })()
      );
    }

    await Promise.all(uploadPromises);

    // 调试：输出最终的图片ID到URL映射
    console.log(`📊 最终图片映射表:`, Array.from(imageIdToUrlMap.entries()));

    // 3. 处理工作表数据
    const parsedSheets = [];

    workbook.eachSheet((worksheet) => {
      const sheetData = {
        name: worksheet.name,
        headers: [],
        jsonData: [],
      };

      // 提取表头
      const headerRow = worksheet.getRow(1);
      if (headerRow?.values) {
        sheetData.headers = headerRow.values.slice(1);
      }

      // 获取当前工作表的图片位置映射
      const positionMap =
        sheetImagePositionMap.get(worksheet.name) || new Map();

      // 处理数据行
      const jsonRows = [];
      worksheet.eachRow((row, rowIndex) => {
        if (rowIndex === 1) return; // 跳过标题行

        const jsonRowData = {};

        row.eachCell({ includeEmpty: true }, (cell, colIndex) => {
          const headerName = sheetData.headers[colIndex - 1] || `列${colIndex}`;

          // 检查是否为图片相关的单元格
          const positionKey = `${rowIndex}-${colIndex}`;
          let imageUrl = null;

          // 只对配图列进行调试
          if (headerName === "配图") {
            console.log(
              `🔍 检查配图单元格: ${worksheet.name} ${positionKey}, 值: "${
                cell.value
              }", 类型: ${typeof cell.value}, 公式: ${cell.formula}`
            );
          }

          // 0. 只在配图列检查HTTP/HTTPS链接
          if (headerName === "配图") {
            const cellValue = cell.value;

            // 检查字符串类型的链接
            if (
              typeof cellValue === "string" &&
              (cellValue.startsWith("http://") ||
                cellValue.startsWith("https://"))
            ) {
              imageUrl = cellValue;
              console.log(
                `🔗 发现直接链接(字符串): ${worksheet.name} ${positionKey} -> ${imageUrl}`
              );
            }
            // 检查对象类型的链接（Excel超链接）
            else if (typeof cellValue === "object" && cellValue !== null) {
              console.log(`🔍 检查对象类型单元格:`, cellValue);

              // 检查是否为Excel超链接对象
              if (cellValue.hyperlink) {
                imageUrl = cellValue.hyperlink;
                console.log(
                  `🔗 发现超链接对象: ${worksheet.name} ${positionKey} -> ${imageUrl}`
                );
              }
              // 检查是否有text属性包含链接
              else if (
                cellValue.text &&
                typeof cellValue.text === "string" &&
                (cellValue.text.startsWith("http://") ||
                  cellValue.text.startsWith("https://"))
              ) {
                imageUrl = cellValue.text;
                console.log(
                  `🔗 发现对象文本链接: ${worksheet.name} ${positionKey} -> ${imageUrl}`
                );
              }
              // 检查其他可能的属性
              else {
                console.log(`🔍 对象属性:`, Object.keys(cellValue));
                // 尝试查找包含http的属性
                for (const [key, value] of Object.entries(cellValue)) {
                  if (
                    typeof value === "string" &&
                    (value.startsWith("http://") ||
                      value.startsWith("https://"))
                  ) {
                    imageUrl = value;
                    console.log(
                      `🔗 发现对象属性链接(${key}): ${worksheet.name} ${positionKey} -> ${imageUrl}`
                    );
                    break;
                  }
                }
              }
            }
            // 3. 如果没有找到HTTP链接，检查是否包含DISPIMG公式
            if (!imageUrl && cell.formula?.includes("DISPIMG")) {
              // 1. 尝试精确位置匹配
              const imageId = positionMap.get(positionKey);
              console.log(
                `🔍 查找图片: ${worksheet.name} ${positionKey}, 找到ID=${imageId}`
              );
              console.log(`🔍 DISPIMG公式内容:`, cell.formula);

              if (imageId !== undefined && imageIdToUrlMap.has(imageId)) {
                imageUrl = imageIdToUrlMap.get(imageId);
                console.log(
                  `📍 精确匹配成功: ${
                    worksheet.name
                  } ${positionKey} -> 图片${imageId} -> ${imageUrl?.substring(
                    0,
                    50
                  )}...`
                );
              }
              // 2. 尝试使用XML解析的映射关系
              else if (dispimgMapping && cell.formula) {
                const dispimgMatch = cell.formula.match(/DISPIMG\("([^"]+)"/);
                if (dispimgMatch) {
                  const imageName = dispimgMatch[1];
                  console.log(`🔍 从公式提取图片名: ${imageName}`);
                  console.log(`🗺️ 当前映射关系:`, dispimgMapping);
                  console.log(`🔍 映射中的键:`, Object.keys(dispimgMapping));

                  // 使用XML解析得到的映射关系
                  const mediaResource = dispimgMapping[imageName];
                  console.log(`🎯 找到的媒体资源:`, mediaResource);

                  if (mediaResource) {
                    // 找到对应的媒体资源在allImages中的索引
                    const mediaIndex = allImages.findIndex(
                      (img) => img.media === mediaResource
                    );
                    console.log(`📍 媒体索引:`, mediaIndex);

                    if (mediaIndex !== -1 && imageIdToUrlMap.has(mediaIndex)) {
                      imageUrl = imageIdToUrlMap.get(mediaIndex);
                      console.log(
                        `📍 XML映射匹配成功: ${
                          worksheet.name
                        } ${positionKey} -> 图片${mediaIndex} (${imageName}) -> ${imageUrl?.substring(
                          0,
                          50
                        )}...`
                      );
                    } else {
                      console.log(
                        `⚠️ 媒体索引无效或URL映射不存在: mediaIndex=${mediaIndex}, hasUrl=${imageIdToUrlMap.has(
                          mediaIndex
                        )}`
                      );
                    }
                  } else {
                    console.log(`⚠️ 在XML映射中未找到图片: ${imageName}`);
                  }
                }
              }
              // 3. 备用方法：尝试根据图片名匹配
              else if (cell.formula) {
                const dispimgMatch = cell.formula.match(/DISPIMG\("([^"]+)"/);
                if (dispimgMatch) {
                  const imageName = dispimgMatch[1];
                  console.log(`🔍 从公式提取图片名: ${imageName}`);

                  // 尝试根据图片名匹配（备用方法）
                  for (let i = 0; i < allImages.length; i++) {
                    const media = allImages[i].media;
                    if (
                      media.name === imageName ||
                      `image${i + 1}` === imageName ||
                      imageName.includes(media.name)
                    ) {
                      if (imageIdToUrlMap.has(i)) {
                        imageUrl = imageIdToUrlMap.get(i);
                        console.log(
                          `📍 名称匹配成功: ${
                            worksheet.name
                          } ${positionKey} -> 图片${i} (${imageName}) -> ${imageUrl?.substring(
                            0,
                            50
                          )}...`
                        );
                        break;
                      }
                    }
                  }
                }
              }

              if (!imageUrl) {
                console.error(
                  `❌ 图片匹配失败: ${worksheet.name} ${positionKey}, 精确ID=${imageId}, 公式=${cell.formula}`
                );
              }
            }

            // 设置配图数据
            if (headerName === "配图") {
              jsonRowData[headerName] = imageUrl
                ? { type: "image", data: imageUrl }
                : null;
            }
          } else {
            // 对于配图列，如果没有检测到链接或DISPIMG，设置为null
            if (headerName === "配图") {
              jsonRowData[headerName] = null;
            } else {
              jsonRowData[headerName] = cell.value || cell.text || null;
            }
          }
        });

        jsonRows.push(jsonRowData);
      });

      sheetData.jsonData = jsonRows;
      parsedSheets.push(sheetData);
    });

    // 4. 生成最终数据
    const finalJsonData = generateBackendJson(parsedSheets);
    parsedData.value = finalJsonData;

    // 5. 上传数据到服务器
    loadingText.value = "正在上传题目数据...";
    await uploadQuestionsToServer(finalJsonData);

    ElMessage.success(`处理成功！共处理 ${finalJsonData.length} 条数据`);
  } catch (err) {
    console.error("解析失败:", err);
    error.value = err.message;
    ElMessage.error(`解析失败: ${err.message}`);
  } finally {
    loading.value = false;
    loadingText.value = "解析中...";
  }
};
// ✅ 生成后端需要的JSON格式数据
function generateBackendJson(parsedSheets) {
  const finalResult = [];
  const errors = [];

  parsedSheets.forEach((sheet) => {
    const questionType = sheet.name; // 判断题、选择题、问答题等

    sheet.jsonData.forEach((row, rowIndex) => {
      const questionData = {
        gameCategory: selectedGameCategory.value, // 使用页面选择的题库
        content: row["问题"] || "",
        imageUrl: null,
        difficulty: row["题目难度"] || "",
        questionType: questionType,
        // correctAnswer: row["正确答案"] || "",
        correctAnswer:
          typeof row["正确答案"] === "string"
            ? row["正确答案"].replace(/\n/g, "")
            : row["正确答案"] || "",
        answerAnalysis: row["答案解析"] || "",
      };

      // 验证必填字段
      const validationErrors = validateQuestionData(
        questionData,
        row,
        questionType
      );

      if (validationErrors.length > 0) {
        errors.push({
          rowIndex: rowIndex + 2, // +2 因为第一行是表头，数组索引从0开始
          sheetName: sheet.name,
          content: questionData.content || "(空)",
          errors: validationErrors,
        });
        return; // 跳过有错误的数据
      }

      // 处理配图
      if (row["配图"] && typeof row["配图"] === "object" && row["配图"].data) {
        questionData.imageUrl = row["配图"].data;
      }

      // 处理选择题的选项
      if (questionType === "选择题" && row["选项"]) {
        const optionsText = row["选项"];
        const optionsList = [];

        // 按行分割选项
        const lines = optionsText.split("\n").filter((line) => line.trim());

        lines.forEach((line) => {
          const trimmedLine = line.trim();
          if (trimmedLine) {
            // 去掉A:、B:、C:、D:等前缀（支持中英文冒号）
            const cleanOption = trimmedLine.replace(/^[A-Z][:：]\s*/, "");
            // 去掉【答案】标记
            const finalOption = cleanOption.replace(/【答案】/g, "").trim();
            if (finalOption) {
              optionsList.push(finalOption);
            }
          }
        });

        questionData.optionsList = optionsList;

        // 调试输出
        console.log(`选择题选项解析: "${optionsText}" -> `, optionsList);
      }

      finalResult.push(questionData);
    });
  });

  // 注意：这里不再设置验证错误，因为预验证已经处理了
  // 如果执行到这里，说明预验证已经通过，不应该有错误
  return finalResult;
}

// ✅ 验证题目数据
function validateQuestionData(questionData, row, questionType) {
  const errors = [];
  console.log(questionData, 11122233);

  // 验证题目问题
  if (
    !questionData.content ||
    (typeof questionData.content === "string" &&
      questionData.content.trim() === "")
  ) {
    errors.push("题目问题不能为空");
  }

  // 验证题目难度
  if (
    !questionData.difficulty ||
    (typeof questionData.difficulty === "string" &&
      questionData.difficulty.trim() === "")
  ) {
    errors.push("题目难度不能为空");
  }

  // 注意：不再验证所属题库，因为会从页面选择的题库统一设置

  // 验证题目类型
  if (
    !questionData.questionType ||
    (typeof questionData.questionType === "string" &&
      questionData.questionType.trim() === "")
  ) {
    errors.push("题目类型不能为空");
  }

  // 验证正确答案
  if (
    !questionData.correctAnswer ||
    (typeof questionData.correctAnswer === "string" &&
      questionData.correctAnswer.trim() === "")
  ) {
    errors.push("正确答案不能为空");
  }

  // 如果是选择题，验证选项
  if (questionType === "选择题") {
    const optionsText = row["选项"];
    if (!optionsText || optionsText.trim() === "") {
      errors.push("选择题的选项不能为空");
    } else {
      // 检查选项是否有内容
      const lines = optionsText.split("\n").filter((line) => line.trim());
      const validOptions = lines.filter((line) => {
        const trimmedLine = line.trim();
        if (trimmedLine) {
          const cleanOption = trimmedLine.replace(/^[A-Z][:：]\s*/, "");
          const finalOption = cleanOption.replace(/【答案】/g, "").trim();
          return finalOption !== "";
        }
        return false;
      });

      if (validOptions.length < 2) {
        errors.push("选择题至少需要2个有效选项");
      }
    }
  }

  return errors;
}

// ✅ 统计图片数量
function countImages(jsonData) {
  let count = 0;
  jsonData.forEach((row) => {
    Object.values(row).forEach((cell) => {
      if (cell && typeof cell === "object" && cell.type === "image") {
        count++;
      }
    });
  });
  return count;
}

// ✅ 导出JSON数据的方法
const exportJsonData = () => {
  if (parsedData.value.length === 0) {
    error.value = "没有数据可导出";
    return;
  }

  // 创建下载链接
  const dataStr = JSON.stringify(parsedData.value, null, 2);
  const dataBlob = new Blob([dataStr], { type: "application/json" });
  const url = URL.createObjectURL(dataBlob);

  const link = document.createElement("a");
  link.href = url;
  link.download = `questions_data_${new Date().getTime()}.json`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);

  console.log("📁 JSON数据已导出");
};

// 文件处理辅助函数
const readFileAsArrayBuffer = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => resolve(e.target.result);
    reader.onerror = (error) => reject(error);
    reader.readAsArrayBuffer(file);
  });
};

const arrayBufferToBase64 = (buffer) => {
  let binary = "";
  const bytes = new Uint8Array(buffer);
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return window.btoa(binary);
};

// ✅ 解析Excel文件内部XML结构，获取DISPIMG图片映射关系
const parseExcelInternalStructure = async (arrayBuffer, workbook) => {
  try {
    console.log("🔍 开始解析Excel内部XML结构...");

    // 使用JSZip解压Excel文件
    const zip = new JSZip();
    const zipContent = await zip.loadAsync(arrayBuffer);

    // 列出所有文件以便调试
    console.log("📁 Excel文件内容:", Object.keys(zipContent.files));

    // 检查是否存在cellimages相关文件
    const cellimagesXmlFile = zipContent.file("xl/cellimages.xml");
    const cellimagesRelsFile = zipContent.file("xl/_rels/cellimages.xml.rels");

    console.log("📄 cellimages.xml存在:", !!cellimagesXmlFile);
    console.log("📄 cellimages.xml.rels存在:", !!cellimagesRelsFile);

    if (!cellimagesXmlFile || !cellimagesRelsFile) {
      console.log("📝 未找到cellimages相关文件，使用传统方法");
      return null;
    }

    // 读取XML文件内容
    const cellimagesXmlContent = await cellimagesXmlFile.async("text");
    const cellimagesRelsContent = await cellimagesRelsFile.async("text");

    console.log(
      "📄 cellimages.xml内容:",
      cellimagesXmlContent.substring(0, 500)
    );
    console.log(
      "📄 cellimages.xml.rels内容:",
      cellimagesRelsContent.substring(0, 500)
    );

    // 使用DOMParser解析XML内容
    const parser = new DOMParser();
    const cellimagesDoc = parser.parseFromString(
      cellimagesXmlContent,
      "text/xml"
    );
    const cellimagesRelsDoc = parser.parseFromString(
      cellimagesRelsContent,
      "text/xml"
    );

    console.log("🔍 解析后的cellimages文档:", cellimagesDoc);
    console.log("🔍 解析后的cellimagesRels文档:", cellimagesRelsDoc);

    // 建立图片映射关系
    const cellimagesNameRidMap = {};
    const result = {};

    // 处理cellimages.xml中的图片信息
    const cellImageElements = cellimagesDoc.querySelectorAll(
      "etc\\:cellImage, cellImage"
    );
    console.log(`🔍 找到 ${cellImageElements.length} 个cellImage元素`);

    cellImageElements.forEach((cellImage) => {
      try {
        // 查找blip元素获取r:embed属性
        const blipElement = cellImage.querySelector("a\\:blip, blip");
        const cNvPrElement = cellImage.querySelector("xdr\\:cNvPr, cNvPr");

        if (blipElement && cNvPrElement) {
          const rid = blipElement.getAttribute("r:embed");
          const imageId = cNvPrElement.getAttribute("name");

          if (rid && imageId) {
            cellimagesNameRidMap[rid] = imageId;
            console.log(`🗺️ 图片映射: ${rid} -> ${imageId}`);
          }
        }
      } catch (e) {
        console.warn("⚠️ 解析图片信息时出错:", e);
      }
    });

    // 关联媒体资源
    const relationshipElements =
      cellimagesRelsDoc.querySelectorAll("Relationship");
    console.log(`🔍 找到 ${relationshipElements.length} 个Relationship元素`);

    relationshipElements.forEach((relationship) => {
      try {
        const target = relationship.getAttribute("Target");
        const id = relationship.getAttribute("Id");

        if (target && id) {
          const data = target.match(
            /media\/([a-zA-Z0-9]+[.][a-zA-Z0-9]{3,4})$/
          );
          if (!data) return;

          const fullName = data[1];
          const [name, extension] = fullName.split(".");
          const imageId = cellimagesNameRidMap[id];

          console.log(`🔗 处理关系: ${id} -> ${target} -> ${imageId}`);

          if (imageId) {
            // 在workbook.media中查找对应的媒体资源
            const mediaResource = workbook.media.find((v) => v.name === name);
            if (mediaResource) {
              result[imageId] = mediaResource;
              console.log(`✅ 成功关联: ${imageId} -> ${name}.${extension}`);
            } else {
              console.log(`⚠️ 未找到媒体资源: ${name}`);
            }
          }
        }
      } catch (e) {
        console.warn("⚠️ 关联媒体资源时出错:", e);
      }
    });

    console.log("🎯 最终的图片映射结果数量:", Object.keys(result).length);
    console.log("🗺️ 完整映射结果:", result);
    return result;
  } catch (error) {
    console.error("❌ 解析Excel内部结构失败:", error);
    return null;
  }
};

// ✅ 生成ArrayBuffer的简单哈希值，用于识别重复图片
const generateImageHash = (buffer) => {
  const bytes = new Uint8Array(buffer);
  let hash = 0;

  // 使用简单的哈希算法
  for (let i = 0; i < bytes.length; i++) {
    hash = ((hash << 5) - hash + bytes[i]) & 0xffffffff;
  }

  // 为了更好的唯一性，也考虑文件大小
  return `${hash}_${bytes.length}`;
};

// ✅ 将ArrayBuffer转换为File对象，可以直接上传
const arrayBufferToFile = (buffer, fileName, mimeType) => {
  const blob = new Blob([buffer], { type: mimeType });
  return new File([blob], fileName, { type: mimeType });
};

// ✅ 将ArrayBuffer转换为Blob对象，也可以上传
const arrayBufferToBlob = (buffer, mimeType) => {
  return new Blob([buffer], { type: mimeType });
};

// ✅ 上传图片到OSS的函数 - 参考upload.vue的实现
const uploadImageToOSS = async (fileObject, fileName) => {
  try {
    // 1. 获取STS Token，参考upload.vue的beforeUpload方法
    const response = await stsTokenApi();
    console.log("获取STS Token成功:", response);

    const ext = fileName.split(".").pop();
    const baseFileName = response.data.fileName;

    // 2. 构建OSS上传参数，参考upload.vue的dataObj
    const dataObj = {
      policy: response.data.policy,
      signature: response.data.signature,
      key: response.data.dir + `/${baseFileName}.${ext}`,
      ossaccessKeyId: response.data.accessKeyId,
      dir: response.data.dir,
      host: response.data.host,
      success_action_status: "200",
    };

    console.log("设置的上传参数:", dataObj);

    // 3. 构建FormData，参考upload.vue的customUpload方法
    const formData = new FormData();

    // 添加OSS必需的字段
    Object.keys(dataObj).forEach((key) => {
      if (dataObj[key]) {
        formData.append(key, dataObj[key]);
      }
    });

    // 添加文件，注意file字段要放在最后
    formData.append("file", fileObject);

    console.log("FormData 内容:");
    for (let [key, value] of formData.entries()) {
      console.log(key, value);
    }

    // 4. 发送请求到OSS，参考upload.vue的ossUploadUrl
    const ossUploadUrl = "https://images2.kkzhw.com";
    const uploadResponse = await fetch(ossUploadUrl, {
      method: "POST",
      body: formData,
    });

    if (uploadResponse.ok) {
      // 5. 构建完整的URL，参考upload.vue的handleUploadSuccess
      const fullUrl = `${dataObj.host}/${dataObj.key}`;

      console.log("生成的图片URL:", fullUrl);

      return {
        success: true,
        url: fullUrl,
        fileName: fileName,
        originalResponse: {
          dataObj: dataObj,
          status: uploadResponse.status,
        },
      };
    } else {
      throw new Error(
        `上传失败: ${uploadResponse.status} ${uploadResponse.statusText}`
      );
    }
  } catch (error) {
    console.error("OSS上传失败:", error);
    return {
      success: false,
      error: error.message,
      fileName: fileName,
    };
  }
};

// ✅ 上传图片到服务器的示例函数
const uploadImageToServer = async (imageInfo, uploadUrl) => {
  try {
    const formData = new FormData();

    // 方式1：使用File对象上传
    formData.append("file", imageInfo.file);

    // 方式2：使用Blob对象上传（如果需要自定义文件名）
    // formData.append('file', imageInfo.blob, imageInfo.name);

    // 添加其他参数
    formData.append("fileName", imageInfo.name);
    formData.append("mimeType", imageInfo.type);

    const response = await fetch(uploadUrl, {
      method: "POST",
      body: formData,
    });

    if (response.ok) {
      const result = await response.json();
      console.log("✅ 图片上传成功:", result);
      return result;
    } else {
      throw new Error(`上传失败: ${response.status}`);
    }
  } catch (error) {
    console.error("❌ 图片上传失败:", error);
    throw error;
  }
};

// ✅ 批量上传所有图片的示例函数
const uploadAllImages = async (parsedSheets, uploadUrl) => {
  const uploadResults = [];

  for (const sheet of parsedSheets) {
    for (const row of sheet.rows) {
      for (const cell of row) {
        if (cell.type === "image" && cell.imageInfo) {
          try {
            const result = await uploadImageToServer(cell.imageInfo, uploadUrl);
            uploadResults.push({
              position: cell.position,
              uploadResult: result,
            });
          } catch (error) {
            uploadResults.push({
              position: cell.position,
              error: error.message,
            });
          }
        }
      }
    }
  }

  return uploadResults;
};

// 组件挂载时获取题库选项
onMounted(() => {
  getGameCategoryOptions();
});
</script>

<style scoped>
/* 样式保持不变 */
.cell-image {
  max-width: 120px;
  max-height: 100px;
  display: block;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.image-error {
  color: #e74c3c;
  font-size: 0.9rem;
  padding: 8px;
  background: #ffeaea;
  border-radius: 4px;
  border: 1px solid #f5c6cb;
}

.controls {
  display: flex;
  gap: 20px;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

/* 题库选择器样式 */
.category-select-wrapper {
  position: relative;
}

.export-btn {
  background: #28a745;
}

.export-btn:hover {
  background: #218838;
}

.debug-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  border-left: 4px solid #007bff;
}

.debug-info h4 {
  margin: 0 0 10px 0;
  color: #007bff;
  font-size: 1.1rem;
}

.debug-info p {
  margin: 5px 0;
  font-size: 0.9rem;
  color: #6c757d;
}

.json-preview {
  margin-top: 20px;
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.json-preview h4 {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 1.1rem;
}

.json-preview pre {
  background: #ffffff;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #dee2e6;
  font-size: 0.8rem;
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.converted-data-preview {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 2px solid #e9ecef;
}

.converted-data-preview h3 {
  color: #28a745;
  border-bottom-color: #28a745;
}

.excel-viewer {
  max-width: 800px;
  margin: 0 auto;
}

.error {
  color: #e74c3c;
  background: #fdeded;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 10px;
  font-weight: 500;
}
.successText {
  color: #04d023;
  background: #d3f7d7;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 10px;
  font-weight: 500;
}
.validation-errors {
  margin: 20px 0;
}

.validation-errors h3 {
  color: #e74c3c !important;
  border-bottom-color: #e74c3c !important;
  margin-bottom: 15px;
}

.sheet-tabs {
  display: flex;
  gap: 10px;
  margin: 20px 0;
  flex-wrap: wrap;
}

.sheet-tabs button {
  padding: 8px 16px;
  background: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.sheet-tabs button.active {
  background: #2196f3;
  color: white;
  border-color: #2196f3;
}

.sheet-content {
  margin-top: 20px;
}

h3 {
  font-size: 1.5rem;
  margin-bottom: 15px;
  color: #2c3e50;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

.table-container {
  overflow-x: auto;
  border: 1px solid #eaeaea;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

table {
  width: 100%;
  border-collapse: collapse;
  min-width: 800px;
}

th {
  background: #3498db;
  color: white;
  padding: 12px 15px;
  text-align: left;
  font-weight: 600;
}

td {
  padding: 10px 15px;
  border-bottom: 1px solid #eaeaea;
  vertical-align: middle;
}

tr:nth-child(even) {
  background-color: #f8f9fa;
}

tr:hover {
  background-color: #e3f2fd;
}

/* 文件上传样式 */
.file-upload-wrapper {
  position: relative;
}

.file-input-hidden {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
  overflow: hidden;
}

.file-upload-btn {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  padding: 12px 24px;
  background: rgba(255, 255, 255, 0.95);
  color: #4a5568;
  border: 2px dashed #cbd5e0;
  border-radius: 12px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  min-width: 200px;
  justify-content: center;
}

.file-upload-btn:hover:not(.disabled) {
  background: rgba(255, 255, 255, 1);
  border-color: #667eea;
  color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}

.file-upload-btn.disabled {
  background: rgba(255, 255, 255, 0.5);
  color: #eaeaea;
  border-color: #e2e8f0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.upload-icon {
  width: 20px;
  height: 20px;
  stroke-width: 2;
}

/* 解析按钮样式 */
.parse-btn {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  padding: 14px 28px;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
  min-width: 160px;
  justify-content: center;
}

.parse-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 172, 254, 0.6);
}

.parse-btn:disabled {
  background: linear-gradient(135deg, #a0aec0 0%, #cbd5e0 100%);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.parse-btn.loading {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.parse-icon,
.btn-spinner {
  width: 18px;
  height: 18px;
  stroke-width: 2;
}

.btn-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Loading 指示器样式 */
.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 20px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  margin: 20px 0;
  color: #495057;
  font-weight: 500;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #dee2e6;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
