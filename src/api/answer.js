import request from '@/utils/request';

// 题目相关API
export function fetchQuestionList(params) {
  return request({
    url: '/openapi/quiz/question/list',
    method: 'get',
    params: params
  });
}

export function createQuestion(data) {
  return request({
    url: '/openapi/quiz/question/create',
    method: 'post',
    data: data
  });
}

// 批量创建题目
export function batchCreateQuestions(questions) {
  return request({
    url: '/openapi/quiz/question/batch/create',
    method: 'post',
    data: { questions }
  });
}

export function updateQuestion(id, data) {
  return request({
    url: '/openapi/quiz/question/update/' + id,
    method: 'post',
    data: data
  });
}

export function deleteQuestion(id) {
  return request({
    url: '/openapi/quiz/question/delete/' + id,
    method: 'post'
  });
}

export function updateQuestionStatus(id, status) {
  return request({
    url: '/openapi/quiz/question/update/status/' + id,
    method: 'post',
    params: { status: status }
  });
}

export function getQuestionDetail(id) {
  return request({
    url: '/openapi/quiz/question/' + id,
    method: 'get'
  });
}

export function getGameCategories() {
  return request({
    url: '/openapi/quiz/question/gameCategories',
    method: 'get'
  });
}

export function stsTokenApi() {
  return request({
    url: '/openapi/quiz/oss/policy',
    method: 'get',
  });
}
export function setQuizBind(data) {
  return request({
    url: '/openapi/quiz/bind',
    method: 'post',
    data
  });
}

// 题库分类相关API
export function fetchCategoryList(params) {
  return request({
    url: '/openapi/quiz/question/gameCategories',
    method: 'get',
    params: params
  });
}

export function createCategory(data) {
  return request({
    url: '/openapi/quiz/category/create',
    method: 'post',
    data: data
  });
}

export function updateCategory(id, data) {
  return request({
    url: '/openapi/quiz/question/category/update',
    method: 'post',
    data: { ...data, id }
  });
}

export function deleteCategory(id) {
  return request({
    url: '/openapi/quiz/question/category/delete',
    method: 'post',
    data: { id }
  });
}


export function getGameLevels(name,data) {
  return request({
    url: `/openapi/quiz/game/${name}/levels`,
    method: 'post',
    data
  });
}

export function getCategoriesList(params) {
  return request({
    url: `/openapi/quiz/question/gameCategories`,
    method: 'get',
    params
  });
}

// 导入题库Excel
export function importQuestionExcel(formData) {
  return request({
    url: '/openapi/quiz/import/excel',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}
