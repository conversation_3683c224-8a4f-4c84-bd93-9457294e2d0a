import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../view/index.vue'

const routes = [
  {
    path: '/',
    name: 'home',
    component: HomeView
  },
  {
    path: '/content',
    name: 'content',
    component: () => import('../view/content.vue') // 懒加载
  },
  {
    path: '/page',
    name: 'page',
    component: () => import('../view/page.vue') // 懒加载
  }
  ,
  {
    path: '/clearing',
    name: 'clearing',
    component: () => import('../view/clearing.vue') // 懒加载
  },
  {
    path: '/questions',
    name: 'questions',
    component: () => import('../view/questions.vue') // 懒加载
  },
  {
    path: '/setting',
    name: 'setting',
    component: () => import('../view/setting.vue') // 懒加载
  }
  ,{
    path: '/answerList',
    name: 'answerList',
    component: () => import('../view/answerList.vue') // 懒加载
  }
  ,{
    path: '/userSetting',
    name: 'userSetting',
    component: () => import('../view/userSetting.vue') // 懒加载
  } ,{
    path: '/index3',
    name: 'index3',
    component: () => import('../view/index3.vue') // 懒加载
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

export default router