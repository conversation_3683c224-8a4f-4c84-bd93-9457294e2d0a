// Learn more about <PERSON><PERSON> commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn set_window_width(window: tauri::Window, width: u32) -> Result<(), String> {
    // 直接使用传入的 window 实例设置尺寸（无需 get_window）
    window.set_size(tauri::LogicalSize { 
        width, 
        height: 900  // 保持原高度不变
    }).map_err(|e| e.to_string())
}

#[tauri::command]
fn get_app_version() -> String {
    // 从 Cargo.toml 中读取版本号
    env!("CARGO_PKG_VERSION").to_string()
}


#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .invoke_handler(tauri::generate_handler![
            set_window_width,
            get_app_version
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
